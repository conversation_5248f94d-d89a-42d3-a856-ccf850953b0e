package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.net.Uri
import android.util.Log
import android.util.LruCache
import androidx.core.graphics.ColorUtils
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 专辑封面缓存工具类
 * 提供内存缓存和磁盘缓存功能
 */
class AlbumArtCache(private val context: Context) {

    companion object {
        private const val TAG = "AlbumArtCache"
        private const val MEMORY_CACHE_SIZE = 20 // 内存缓存大小
        private const val BLUR_CACHE_SIZE = 10 // 模糊图片缓存大小
        private const val PALETTE_CACHE_SIZE = 30 // 调色板缓存大小

        // 单例实例
        @Volatile
        private var INSTANCE: AlbumArtCache? = null

        /**
         * 获取AlbumArtCache实例
         * @param context 上下文
         * @return AlbumArtCache实例
         */
        fun getInstance(context: Context? = null): AlbumArtCache {
            return INSTANCE ?: synchronized(this) {
                val ctx = context?.applicationContext ?: throw IllegalArgumentException("Context不能为空")
                INSTANCE ?: AlbumArtCache(ctx).also { INSTANCE = it }
            }
        }

        /**
         * 从调色板提取颜色信息
         * @param palette 调色板
         * @return 颜色信息
         */
        fun extractColorInfo(palette: Palette): ColorInfo {
            val dominantColor = palette.getDominantColor(Color.BLACK)
            val vibrantColor = palette.getVibrantColor(dominantColor)
            val textColor = if (ColorUtils.calculateLuminance(dominantColor) > 0.5) {
                Color.BLACK
            } else {
                Color.WHITE
            }

            return ColorInfo(dominantColor, vibrantColor, textColor)
        }
    }

    // 内存缓存
    private val memoryCache = object : LruCache<String, Bitmap>(MEMORY_CACHE_SIZE) {
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            // 返回图片大小（KB）
            return bitmap.byteCount / 1024
        }
    }

    // 模糊图片缓存
    private val blurCache = object : LruCache<String, Bitmap>(BLUR_CACHE_SIZE) {
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            // 返回图片大小（KB）
            return bitmap.byteCount / 1024
        }
    }

    // 调色板缓存
    private val paletteCache = LruCache<String, Palette>(PALETTE_CACHE_SIZE)

    // 磁盘缓存目录
    private val albumArtCacheDir: File by lazy {
        File(context.cacheDir, "album_art").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    // 模糊图片缓存目录
    private val blurCacheDir: File by lazy {
        File(context.cacheDir, "album_art_blur").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    /**
     * 获取专辑封面
     * 先从内存缓存获取，如果没有则从磁盘缓存获取，如果还没有则从网络获取
     * @param uri 专辑封面URI
     * @param songId 歌曲ID，用于缓存键
     * @return 专辑封面位图，如果获取失败则返回null
     */
    suspend fun getAlbumArt(uri: Uri?, songId: Long): Bitmap? = withContext(Dispatchers.IO) {
        if (uri == null) {
            return@withContext null
        }

        val cacheKey = "$songId"

        // 先从内存缓存获取
        var bitmap = memoryCache.get(cacheKey)
        if (bitmap != null) {
            Log.d(TAG, "从内存缓存获取专辑封面: $cacheKey")
            return@withContext bitmap
        }

        // 从磁盘缓存获取
        bitmap = readFromDisk(cacheKey)
        if (bitmap != null) {
            // 放入内存缓存
            memoryCache.put(cacheKey, bitmap)
            Log.d(TAG, "从磁盘缓存获取专辑封面: $cacheKey")
            return@withContext bitmap
        }

        // 从网络获取
        try {
            bitmap = Glide.with(context)
                .asBitmap()
                .load(uri)
                .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
                .submit()
                .get()

            if (bitmap != null) {
                // 保存到缓存
                memoryCache.put(cacheKey, bitmap)
                saveToDisk(cacheKey, bitmap)
                Log.d(TAG, "从网络获取专辑封面: $cacheKey")
                return@withContext bitmap
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取专辑封面失败: $uri", e)
        }

        return@withContext null
    }

    /**
     * 获取模糊专辑封面
     * 先从内存缓存获取，如果没有则从磁盘缓存获取，如果还没有则生成
     * @param uri 专辑封面URI
     * @param songId 歌曲ID，用于缓存键
     * @param radius 模糊半径
     * @return 模糊专辑封面位图，如果获取失败则返回null
     */
    suspend fun getBlurredAlbumArt(uri: Uri?, songId: Long, radius: Int = 25): Bitmap? = withContext(Dispatchers.IO) {
        if (uri == null) {
            return@withContext null
        }

        val cacheKey = "${songId}_blur_$radius"

        // 先从内存缓存获取
        var bitmap = blurCache.get(cacheKey)
        if (bitmap != null) {
            Log.d(TAG, "从内存缓存获取模糊专辑封面: $cacheKey")
            return@withContext bitmap
        }

        // 从磁盘缓存获取
        bitmap = readFromDisk(cacheKey, isBlur = true)
        if (bitmap != null) {
            // 放入内存缓存
            blurCache.put(cacheKey, bitmap)
            Log.d(TAG, "从磁盘缓存获取模糊专辑封面: $cacheKey")
            return@withContext bitmap
        }

        // 获取原始专辑封面
        val originalBitmap = getAlbumArt(uri, songId)
        if (originalBitmap != null) {
            // 生成模糊图片
            bitmap = ImageUtils.blurBitmap(context, originalBitmap, radius)
            // 移除不必要的null检查，因为blurBitmap可能返回null
            bitmap?.let {
                // 保存到缓存
                blurCache.put(cacheKey, it)
                saveToDisk(cacheKey, it, isBlur = true)
                Log.d(TAG, "生成模糊专辑封面: $cacheKey")
                return@withContext it
            }
        }

        return@withContext null
    }

    /**
     * 获取专辑封面调色板
     * 先从内存缓存获取，如果没有则生成
     * @param uri 专辑封面URI
     * @param songId 歌曲ID，用于缓存键
     * @return 调色板，如果获取失败则返回null
     */
    suspend fun getAlbumArtPalette(uri: Uri?, songId: Long): Palette? = withContext(Dispatchers.IO) {
        if (uri == null) {
            return@withContext null
        }

        val cacheKey = "${songId}_palette"

        // 先从内存缓存获取
        var palette = paletteCache.get(cacheKey)
        if (palette != null) {
            Log.d(TAG, "从内存缓存获取调色板: $cacheKey")
            return@withContext palette
        }

        // 获取原始专辑封面
        val bitmap = getAlbumArt(uri, songId)
        if (bitmap != null) {
            // 生成调色板
            try {
                palette = Palette.from(bitmap).generate()
                // Palette.generate()总是返回非null值，移除不必要的检查
                // 保存到内存缓存
                paletteCache.put(cacheKey, palette)
                Log.d(TAG, "生成调色板: $cacheKey")
                return@withContext palette
            } catch (e: Exception) {
                Log.e(TAG, "生成调色板失败: $uri", e)
            }
        }

        return@withContext null
    }

    /**
     * 从磁盘读取专辑封面
     * @param cacheKey 缓存键
     * @param isBlur 是否是模糊图片
     * @return 专辑封面位图，如果没有缓存则返回null
     */
    private fun readFromDisk(cacheKey: String, isBlur: Boolean = false): Bitmap? {
        val cacheDir = if (isBlur) blurCacheDir else albumArtCacheDir
        val file = File(cacheDir, "$cacheKey.jpg")
        if (!file.exists()) {
            return null
        }

        try {
            return BitmapFactory.decodeFile(file.absolutePath)
        } catch (e: Exception) {
            Log.e(TAG, "读取专辑封面缓存失败: $cacheKey", e)
            // 如果读取失败，删除可能损坏的文件
            file.delete()
            return null
        }
    }

    /**
     * 保存专辑封面到磁盘
     * @param cacheKey 缓存键
     * @param bitmap 专辑封面位图
     * @param isBlur 是否是模糊图片
     */
    fun saveToDisk(cacheKey: String, bitmap: Bitmap, isBlur: Boolean = false) {
        val cacheDir = if (isBlur) blurCacheDir else albumArtCacheDir
        val file = File(cacheDir, "$cacheKey.jpg")

        try {
            FileOutputStream(file).use { output ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, output)
                output.flush()
            }
        } catch (e: IOException) {
            Log.e(TAG, "保存专辑封面缓存失败: $cacheKey", e)
            // 如果保存失败，删除可能损坏的文件
            if (file.exists()) {
                file.delete()
            }
        }
    }

    /**
     * 清除缓存
     * @param songId 歌曲ID，如果为null则清除所有缓存
     */
    suspend fun clearCache(songId: Long? = null) = withContext(Dispatchers.IO) {
        if (songId != null) {
            // 清除指定歌曲的缓存
            val cacheKey = "$songId"

            // 清除内存缓存
            memoryCache.remove(cacheKey)
            blurCache.remove("${cacheKey}_blur_25")
            paletteCache.remove("${cacheKey}_palette")

            // 清除磁盘缓存
            val albumArtFile = File(albumArtCacheDir, "$cacheKey.jpg")
            if (albumArtFile.exists()) {
                albumArtFile.delete()
            }

            val blurFile = File(blurCacheDir, "${cacheKey}_blur_25.jpg")
            if (blurFile.exists()) {
                blurFile.delete()
            } else {
                // 文件不存在，无需删除
                Log.d(TAG, "模糊图片缓存文件不存在: ${blurFile.absolutePath}")
            }
        } else {
            // 清除所有缓存
            memoryCache.evictAll()
            blurCache.evictAll()
            paletteCache.evictAll()

            albumArtCacheDir.listFiles()?.forEach { it.delete() }
            blurCacheDir.listFiles()?.forEach { it.delete() }
        }
    }

    /**
     * 获取专辑封面
     * @param url 专辑封面URL
     * @return 专辑封面位图，如果获取失败则返回null
     */
    fun getAlbumArt(url: String?): Bitmap? {
        if (url.isNullOrEmpty()) {
            return null
        }

        // 从内存缓存获取
        return memoryCache.get(url)
    }

    /**
     * 保存专辑封面
     * @param url 专辑封面URL
     * @param bitmap 专辑封面位图
     */
    fun putAlbumArt(url: String, bitmap: Bitmap) {
        if (url.isNotEmpty() && bitmap.isRecycled.not()) {
            memoryCache.put(url, bitmap)

            // 异步保存到磁盘
            val file = File(albumArtCacheDir, "${url.hashCode()}.jpg")
            Thread {
                try {
                    FileOutputStream(file).use { output ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, output)
                        output.flush()
                    }
                } catch (e: IOException) {
                    Log.e(TAG, "保存专辑封面缓存失败: $url", e)
                }
            }.start()
        }
    }

    /**
     * 获取颜色信息
     * @param url 专辑封面URL
     * @return 颜色信息，如果获取失败则返回null
     */
    fun getColorInfo(url: String?): ColorInfo? {
        if (url.isNullOrEmpty()) {
            return null
        }

        // 从内存缓存获取调色板
        val palette = paletteCache.get("${url.hashCode()}")
        return if (palette != null) {
            extractColorInfo(palette)
        } else {
            null
        }
    }

    /**
     * 保存颜色信息
     * @param url 专辑封面URL
     * @param colorInfo 颜色信息
     */
    fun putColorInfo(url: String, @Suppress("UNUSED_PARAMETER") colorInfo: ColorInfo) {
        // 创建调色板
        val palette = Palette.Builder(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888)).generate()

        // 保存到缓存
        paletteCache.put("${url.hashCode()}", palette)
    }
}

/**
 * 颜色信息
 * @param dominantColor 主色调
 * @param vibrantColor 鲜艳的颜色
 * @param textColor 文本颜色
 */
data class ColorInfo(
    val dominantColor: Int,
    val vibrantColor: Int,
    val textColor: Int
)
