package com.example.aimusicplayer.api;

import android.content.Context;
import android.util.Log;
import android.util.LruCache;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.example.aimusicplayer.utils.NetworkUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import java.io.File;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import javax.inject.Inject;
import javax.inject.Singleton;

import okhttp3.Cache;
import okhttp3.Call;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Callback;
import retrofit2.HttpException;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * API管理器
 * 统一的API调用入口，提供高效、易用的API调用方式
 * 支持缓存、重试、错误处理、请求取消等功能
 *
 * 特性：
 * - 使用Hilt依赖注入，遵循SOLID原则
 * - 统一错误处理和友好提示
 * - 智能缓存策略（内存缓存+磁盘缓存）
 * - 自动重试机制
 * - 请求生命周期管理
 * - 网络状态感知
 * - 性能监控
 */
@Singleton
public class ApiManager {
    private static final String TAG = "ApiManager";

    // 基础URL
    private static final String BASE_URL = "https://zm.armoe.cn/";

    // 缓存配置
    private static final long CACHE_SIZE = 30 * 1024 * 1024; // 30MB
    private static final int DEFAULT_CACHE_TIME = 60; // 默认缓存60秒
    private static final int LONG_CACHE_TIME = 60 * 60; // 长缓存1小时
    private static final int VERY_LONG_CACHE_TIME = 60 * 60 * 24; // 超长缓存1天
    private static final int PERMANENT_CACHE_TIME = 60 * 60 * 24 * 7; // 永久缓存7天

    // 重试配置
    private static final int MAX_RETRY_COUNT = 3;
    private static final long RETRY_INTERVAL = 1000; // 1秒
    private static final long MAX_RETRY_INTERVAL = 5000; // 最大重试间隔5秒

    // 超时配置
    private static final int CONNECT_TIMEOUT = 15; // 15秒
    private static final int READ_TIMEOUT = 20; // 20秒
    private static final int WRITE_TIMEOUT = 20; // 20秒

    // Retrofit实例
    private Retrofit retrofit;

    // 应用上下文
    private Context appContext;

    // 内存缓存
    private LruCache<String, Object> memoryCache;

    // 活跃请求映射表，用于取消请求
    private final Map<String, retrofit2.Call<?>> activeCallsMap = new ConcurrentHashMap<>();

    // 错误消息映射表，将错误码映射到友好的错误消息
    private final Map<Integer, String> errorMessageMap = new HashMap<>();

    // 统计信息
    private final AtomicInteger totalRequestCount = new AtomicInteger(0);
    private final AtomicInteger successRequestCount = new AtomicInteger(0);
    private final AtomicInteger failedRequestCount = new AtomicInteger(0);
    private final AtomicInteger cachedResponseCount = new AtomicInteger(0);
    private final Map<String, Long> apiResponseTimes = new ConcurrentHashMap<>();

    /**
     * 构造函数，使用Hilt依赖注入
     *
     * @param context 应用上下文
     */
    @Inject
    public ApiManager(Context context) {
        // 保存应用上下文
        this.appContext = context.getApplicationContext();

        // 初始化内存缓存（最多缓存100个对象）
        memoryCache = new LruCache<>(100);

        // 初始化错误消息映射表
        initErrorMessageMap();

        // 初始化Retrofit
        initRetrofit();
    }

    /**
     * 初始化错误消息映射表
     * 包含HTTP标准错误码和网易云音乐API特定错误码
     */
    private void initErrorMessageMap() {
        // HTTP错误码
        errorMessageMap.put(400, "请求参数错误");
        errorMessageMap.put(401, "未授权，请重新登录");
        errorMessageMap.put(403, "无权限访问该资源");
        errorMessageMap.put(404, "请求的资源不存在");
        errorMessageMap.put(405, "请求方法不允许");
        errorMessageMap.put(406, "请求的格式不可得");
        errorMessageMap.put(408, "请求超时，请稍后重试");
        errorMessageMap.put(409, "请求冲突");
        errorMessageMap.put(410, "请求的资源已被永久删除");
        errorMessageMap.put(413, "请求实体过大");
        errorMessageMap.put(414, "请求的URI过长");
        errorMessageMap.put(415, "不支持的媒体类型");
        errorMessageMap.put(429, "请求过于频繁，请稍后重试");
        errorMessageMap.put(431, "请求头字段太大");
        errorMessageMap.put(500, "服务器内部错误，请稍后重试");
        errorMessageMap.put(501, "服务器不支持请求的功能");
        errorMessageMap.put(502, "网关错误，请稍后重试");
        errorMessageMap.put(503, "服务不可用，请稍后重试");
        errorMessageMap.put(504, "网关超时，请稍后重试");
        errorMessageMap.put(505, "HTTP版本不支持");

        // 网易云音乐API错误码
        errorMessageMap.put(-1, "系统错误");
        errorMessageMap.put(301, "需要登录");
        errorMessageMap.put(302, "账号或密码错误");
        errorMessageMap.put(400, "请求参数错误");
        errorMessageMap.put(404, "请求的资源不存在");
        errorMessageMap.put(501, "参数错误");
        errorMessageMap.put(502, "非法操作");
        errorMessageMap.put(503, "服务不可用");
        errorMessageMap.put(509, "请求过于频繁，请稍后再试");
        errorMessageMap.put(10001, "参数错误");
        errorMessageMap.put(20001, "登录失败");

        // 二维码登录相关错误码
        errorMessageMap.put(800, "二维码已过期，请重新获取");
        errorMessageMap.put(801, "等待扫码");
        errorMessageMap.put(802, "等待确认");
        errorMessageMap.put(803, "授权登录成功");

        // 歌曲相关错误码
        errorMessageMap.put(-110, "歌曲已下架");
        errorMessageMap.put(-105, "歌曲暂时不可用");
        errorMessageMap.put(-104, "歌曲已被删除");
        errorMessageMap.put(-99, "需要购买专辑");
        errorMessageMap.put(-2, "歌曲需要付费，请先购买");

        // 用户相关错误码
        errorMessageMap.put(2001, "用户不存在");
        errorMessageMap.put(2002, "用户已存在");
        errorMessageMap.put(2003, "用户被禁止");

        // 歌单相关错误码
        errorMessageMap.put(3001, "歌单不存在");
        errorMessageMap.put(3002, "歌单已存在");
        errorMessageMap.put(3003, "歌单操作失败");
    }

    /**
     * 初始化Retrofit
     * 配置OkHttp客户端、拦截器、缓存策略和Gson转换器
     */
    private void initRetrofit() {
        // 创建日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(message -> {
            // 限制日志长度，避免超长响应导致日志溢出
            if (message.length() > 4000) {
                Log.d(TAG, "API日志 (截断): " + message.substring(0, 4000) + "...");
            } else {
                Log.d(TAG, message);
            }
        });

        // 根据构建类型设置日志级别
        boolean isDebug = false;
        try {
            isDebug = (appContext.getApplicationInfo().flags
                    & android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE)
                    != 0;
        } catch (Exception e) {
            Log.e(TAG, "获取调试状态失败", e);
        }

        loggingInterceptor.setLevel(isDebug
                ? HttpLoggingInterceptor.Level.BODY
                : HttpLoggingInterceptor.Level.BASIC);

        // 创建性能监控拦截器
        Interceptor performanceInterceptor = chain -> {
            Request request = chain.request();
            String url = request.url().toString();

            // 记录请求开始时间
            long startTime = System.currentTimeMillis();

            // 增加请求计数
            totalRequestCount.incrementAndGet();

            try {
                // 执行请求
                Response response = chain.proceed(request);

                // 计算请求耗时
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                // 记录API响应时间
                apiResponseTimes.put(url, duration);

                // 记录成功请求
                if (response.isSuccessful()) {
                    successRequestCount.incrementAndGet();

                    // 检查是否使用了缓存
                    String cacheHeader = response.header("X-Cache");
                    if (cacheHeader != null && cacheHeader.contains("HIT")) {
                        cachedResponseCount.incrementAndGet();
                    }

                    // 记录慢请求
                    if (duration > 1000) {
                        Log.w(TAG, "慢API请求: " + url + " 耗时: " + duration + "ms");
                    }
                } else {
                    // 记录失败请求
                    failedRequestCount.incrementAndGet();
                    Log.e(TAG, "API请求失败: " + url + " 状态码: " + response.code());
                }

                return response;
            } catch (Exception e) {
                // 记录失败请求
                failedRequestCount.incrementAndGet();
                Log.e(TAG, "API请求异常: " + url, e);
                throw e;
            }
        };

        // 创建错误处理拦截器
        Interceptor errorInterceptor = chain -> {
            Request request = chain.request();
            Response response = chain.proceed(request);

            // 检查响应状态码
            if (!response.isSuccessful()) {
                int code = response.code();
                String message = response.message();
                String url = request.url().toString();

                Log.e(TAG, "API请求失败: " + code + " - " + message + " URL: " + url);

                // 读取错误响应体
                ResponseBody errorBody = response.body();
                if (errorBody != null) {
                    try {
                        String errorJson = errorBody.string();
                        Log.e(TAG, "错误响应: " + errorJson);

                        // 解析错误码和消息
                        try {
                            JsonObject jsonObject = JsonParser.parseString(errorJson).getAsJsonObject();
                            if (jsonObject.has("code")) {
                                int apiCode = jsonObject.get("code").getAsInt();
                                String apiMessage = jsonObject.has("message")
                                        ? jsonObject.get("message").getAsString()
                                        : (jsonObject.has("msg") ? jsonObject.get("msg").getAsString() : "未知错误");

                                // 获取友好错误消息
                                String friendlyMessage = getFriendlyErrorMessage(apiCode, apiMessage);
                                Log.e(TAG, "API错误: 代码=" + apiCode + ", 消息=" + friendlyMessage);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "解析错误响应失败", e);
                        }

                        // 克隆响应体，因为string()方法会关闭原始响应体
                        ResponseBody clonedBody = ResponseBody.create(
                                errorBody.contentType(),
                                errorJson
                        );

                        // 返回带有克隆响应体的响应
                        return response.newBuilder()
                                .body(clonedBody)
                                .build();
                    } catch (IOException e) {
                        Log.e(TAG, "读取错误响应失败", e);
                    }
                }
            }

            return response;
        };

        // 创建重试拦截器，使用指数退避策略
        RetryInterceptor retryInterceptor = new RetryInterceptor(MAX_RETRY_COUNT, RETRY_INTERVAL);

        // 创建Cookie拦截器
        CookieInterceptor cookieInterceptor = new CookieInterceptor();

        // 创建OkHttpClient.Builder
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(read_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(performanceInterceptor) // 首先添加性能监控拦截器
                .addInterceptor(loggingInterceptor)
                .addInterceptor(errorInterceptor)
                .addInterceptor(retryInterceptor)
                .addInterceptor(cookieInterceptor);

        // 添加缓存支持
        if (appContext != null) {
            // 创建缓存目录
            File cacheDir = new File(appContext.getCacheDir(), "http-cache");
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }

            // 创建缓存
            Cache cache = new Cache(cacheDir, CACHE_SIZE);

            // 添加缓存
            builder.cache(cache);

            // 添加缓存拦截器
            builder.addNetworkInterceptor(new CacheInterceptor(appContext, DEFAULT_CACHE_TIME, VERY_LONG_CACHE_TIME) {
                @Override
                public Response intercept(Chain chain) throws IOException {
                    Request request = chain.request();
                    String url = request.url().toString();

                    // 对特定API使用更长的缓存时间
                    if (shouldUseLongCache(url)) {
                        return super.intercept(chain, LONG_CACHE_TIME, VERY_LONG_CACHE_TIME);
                    } else if (shouldUsePermanentCache(url)) {
                        return super.intercept(chain, PERMANENT_CACHE_TIME, PERMANENT_CACHE_TIME);
                    } else {
                        return super.intercept(chain);
                    }
                }

                // 判断是否应该使用长缓存
                private boolean shouldUseLongCache(String url) {
                    // 对于不经常变化的数据使用长缓存
                    return url.contains("/toplist") ||
                           url.contains("/playlist/detail") ||
                           url.contains("/album") ||
                           url.contains("/artist");
                }

                // 判断是否应该使用永久缓存
                private boolean shouldUsePermanentCache(String url) {
                    // 对于几乎不变的数据使用永久缓存
                    return url.contains("/song/detail") ||
                           url.contains("/lyric");
                }
            });
        }

        // 创建OkHttpClient
        OkHttpClient okHttpClient = builder.build();

        // 配置Gson
        Gson gson = new GsonBuilder()
                .setLenient()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") // ISO 8601格式
                .create();

        // 创建Retrofit实例
        retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .client(okHttpClient)
                .build();

        Log.i(TAG, "API管理器初始化完成");
    }

    /**
     * 获取Retrofit实例
     */
    public Retrofit getRetrofit() {
        if (retrofit == null) {
            throw new IllegalStateException("ApiManager未初始化，请先调用init()方法");
        }
        return retrofit;
    }

    /**
     * 检查网络是否可用
     */
    public boolean isNetworkAvailable() {
        return appContext != null && NetworkUtils.isNetworkAvailable(appContext);
    }

    /**
     * 执行API调用（使用ApiCallback）
     * @param call API调用
     * @param responseLiveData 响应LiveData
     * @param errorMessageLiveData 错误信息LiveData
     * @param loadingLiveData 加载状态LiveData
     * @param <T> 响应数据类型
     */
    public <T> void execute(
            retrofit2.Call<T> call,
            MutableLiveData<ApiResponse<T>> responseLiveData,
            MutableLiveData<String> errorMessageLiveData,
            MutableLiveData<Boolean> loadingLiveData) {

        // 生成请求标识符
        String callId = call.request().url().toString();

        // 保存活跃请求
        activeCallsMap.put(callId, call.clone());

        // 检查网络是否可用
        if (!isNetworkAvailable()) {
            if (errorMessageLiveData != null) {
                errorMessageLiveData.postValue("网络不可用，请检查网络设置");
            }
            if (loadingLiveData != null) {
                loadingLiveData.postValue(false);
            }
            if (responseLiveData != null) {
                responseLiveData.postValue(ApiResponse.error("网络不可用，请检查网络设置", -1));
            }
            return;
        }

        // 执行API调用
        call.enqueue(new ApiCallback<T>(responseLiveData, errorMessageLiveData, loadingLiveData, true) {
            @Override
            public void onResponse(@NonNull retrofit2.Call<T> call, @NonNull retrofit2.Response<T> response) {
                // 从活跃请求中移除
                activeCallsMap.remove(callId);

                // 调用父类方法处理响应
                super.onResponse(call, response);
            }

            @Override
            public void onFailure(@NonNull retrofit2.Call<T> call, @NonNull Throwable t) {
                // 从活跃请求中移除
                activeCallsMap.remove(callId);

                // 调用父类方法处理失败
                super.onFailure(call, t);
            }
        });
    }

    /**
     * 执行API调用（简化版）
     * @param call API调用
     * @param responseLiveData 响应LiveData
     * @param errorMessageLiveData 错误信息LiveData
     * @param <T> 响应数据类型
     */
    public <T> void execute(
            retrofit2.Call<T> call,
            MutableLiveData<ApiResponse<T>> responseLiveData,
            MutableLiveData<String> errorMessageLiveData) {
        execute(call, responseLiveData, errorMessageLiveData, null);
    }

    /**
     * 执行API调用（自定义回调）
     * @param call API调用
     * @param callback 回调
     * @param <T> 响应数据类型
     */
    public <T> void execute(retrofit2.Call<T> call, Callback<T> callback) {
        // 生成请求标识符
        String callId = call.request().url().toString();

        // 保存活跃请求
        activeCallsMap.put(callId, call.clone());

        // 检查网络是否可用
        if (!isNetworkAvailable()) {
            callback.onFailure(call, new IOException("网络不可用，请检查网络设置"));
            return;
        }

        // 执行API调用
        call.enqueue(new Callback<T>() {
            @Override
            public void onResponse(@NonNull retrofit2.Call<T> call, @NonNull retrofit2.Response<T> response) {
                // 从活跃请求中移除
                activeCallsMap.remove(callId);

                // 调用原始回调
                callback.onResponse(call, response);
            }

            @Override
            public void onFailure(@NonNull retrofit2.Call<T> call, @NonNull Throwable t) {
                // 从活跃请求中移除
                activeCallsMap.remove(callId);

                // 调用原始回调
                callback.onFailure(call, t);
            }
        });
    }

    /**
     * 取消所有请求
     */
    public void cancelAllRequests() {
        for (retrofit2.Call<?> call : activeCallsMap.values()) {
            if (!call.isCanceled()) {
                call.cancel();
            }
        }
        activeCallsMap.clear();
    }

    /**
     * 取消特定请求
     * @param url 请求URL
     */
    public void cancelRequest(String url) {
        retrofit2.Call<?> call = activeCallsMap.get(url);
        if (call != null && !call.isCanceled()) {
            call.cancel();
            activeCallsMap.remove(url);
        }
    }

    /**
     * 获取友好的错误消息
     * @param code 错误码
     * @param defaultMessage 默认错误消息
     * @return 友好的错误消息
     */
    public String getFriendlyErrorMessage(int code, String defaultMessage) {
        String message = errorMessageMap.get(code);
        return message != null ? message : defaultMessage;
    }

    /**
     * 解析错误响应
     * @param errorBody 错误响应体
     * @return 错误信息和错误码
     */
    public Pair<String, Integer> parseErrorResponse(ResponseBody errorBody) {
        if (errorBody == null) {
            return new Pair<>("未知错误", -1);
        }

        try {
            String errorJson = errorBody.string();
            JsonObject jsonObject = JsonParser.parseString(errorJson).getAsJsonObject();

            int code = -1;
            String message = "未知错误";

            if (jsonObject.has("code")) {
                code = jsonObject.get("code").getAsInt();
            }

            if (jsonObject.has("message")) {
                message = jsonObject.get("message").getAsString();
            } else if (jsonObject.has("msg")) {
                message = jsonObject.get("msg").getAsString();
            }

            // 获取友好的错误消息
            String friendlyMessage = getFriendlyErrorMessage(code, message);

            return new Pair<>(friendlyMessage, code);
        } catch (Exception e) {
            Log.e(TAG, "解析错误响应失败", e);
            return new Pair<>("解析错误响应失败", -1);
        }
    }

    /**
     * 简单的键值对类
     */
    public static class Pair<F, S> {
        public final F first;
        public final S second;

        public Pair(F first, S second) {
            this.first = first;
            this.second = second;
        }
    }
}
