package com.example.aimusicplayer.service

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 播放控制器实现类
 */
@Singleton
class PlayerControllerImpl @Inject constructor(
    private val context: Context
) : PlayerController {
    private val TAG = "PlayerControllerImpl"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private val _playlist = MutableLiveData<List<MediaItem>>(emptyList())
    override val playlist: LiveData<List<MediaItem>> = _playlist

    private val _currentSong = MutableLiveData<MediaItem?>(null)
    override val currentSong: LiveData<MediaItem?> = _currentSong

    private val _playState = MutableStateFlow<PlayState>(PlayState.Idle)
    override val playState: StateFlow<PlayState> = _playState

    private val _playProgress = MutableStateFlow(0L)
    override val playProgress: StateFlow<Long> = _playProgress

    private val _bufferingPercent = MutableStateFlow(0)
    override val bufferingPercent: StateFlow<Int> = _bufferingPercent

    private val _playMode = MutableStateFlow<PlayMode>(PlayMode.Loop)
    override val playMode: StateFlow<PlayMode> = _playMode

    private var player: Player? = null
        get() {
            if (field == null) {
                field = PlayServiceModule.getPlayer()
                if (field != null) {
                    initPlayerListener()
                    Log.d(TAG, "播放器获取成功，已初始化监听器")
                } else {
                    Log.w(TAG, "播放器尚未初始化，返回null")
                }
            }
            return field
        }

    // 添加播放器就绪状态检查
    private fun isPlayerReady(): Boolean {
        return player != null
    }

    // 等待播放器就绪的协程方法
    private suspend fun waitForPlayer(timeoutMs: Long = 5000): Player? {
        val startTime = System.currentTimeMillis()
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val currentPlayer = player
            if (currentPlayer != null) {
                return currentPlayer
            }
            delay(100)
        }
        Log.w(TAG, "等待播放器超时")
        return null
    }

    init {
        // 启动进度更新协程
        scope.launch {
            while (true) {
                updateProgress()
                delay(500)
            }
        }
    }

    private fun updateProgress() {
        player?.let {
            if (it.isPlaying) {
                _playProgress.value = it.currentPosition
                _bufferingPercent.value = it.bufferedPercentage
            }
        }
    }

    private fun initPlayerListener() {
        player?.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_IDLE -> _playState.value = PlayState.Idle
                    Player.STATE_BUFFERING -> _playState.value = PlayState.Preparing
                    Player.STATE_READY -> {
                        if (player?.isPlaying == true) {
                            _playState.value = PlayState.Playing
                        } else {
                            _playState.value = PlayState.Pause
                        }
                    }
                    Player.STATE_ENDED -> next()
                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                if (isPlaying) {
                    _playState.value = PlayState.Playing
                } else if (player?.playbackState == Player.STATE_READY) {
                    _playState.value = PlayState.Pause
                }
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                _currentSong.value = mediaItem
            }
        })
    }

    override fun addAndPlay(song: MediaItem) {
        ensureServiceStarted()
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: mutableListOf()
            currentPlaylist.add(song)
            _playlist.value = currentPlaylist

            it.addMediaItem(song)
            it.prepare()
            it.seekToDefaultPosition(currentPlaylist.size - 1)
            it.play()
        }
    }

    override fun replaceAll(songList: List<MediaItem>, song: MediaItem) {
        try {
            ensureServiceStarted()

            // 添加空值检查和日志
            if (songList.isEmpty()) {
                Log.w(TAG, "replaceAll: 歌曲列表为空")
                return
            }

            val currentPlayer = player
            if (currentPlayer == null) {
                Log.e(TAG, "replaceAll: player为null，等待播放器初始化")
                // 使用协程等待播放器就绪
                scope.launch {
                    val readyPlayer = waitForPlayer(3000) // 等待3秒
                    if (readyPlayer != null) {
                        Log.d(TAG, "replaceAll: 播放器初始化完成，重新尝试")
                        replaceAll(songList, song)
                    } else {
                        Log.e(TAG, "replaceAll: 播放器初始化超时")
                    }
                }
                return
            }

            Log.d(TAG, "replaceAll: 替换播放列表，共${songList.size}首歌曲")

            // 验证歌曲列表
            val validSongs = songList.filter { mediaItem ->
                val isValid = !mediaItem.mediaId.isNullOrEmpty() &&
                             mediaItem.requestMetadata.mediaUri != null
                if (!isValid) {
                    Log.w(TAG, "replaceAll: 发现无效歌曲，mediaId=${mediaItem.mediaId}, uri=${mediaItem.requestMetadata.mediaUri}")
                }
                isValid
            }

            if (validSongs.isEmpty()) {
                Log.e(TAG, "replaceAll: 没有有效的歌曲")
                return
            }

            _playlist.value = validSongs

            // 安全地清空和设置媒体项
            try {
                currentPlayer.clearMediaItems()
                currentPlayer.setMediaItems(validSongs)
                currentPlayer.prepare()

                val index = validSongs.indexOfFirst { item -> item.mediaId == song.mediaId }
                if (index >= 0) {
                    currentPlayer.seekToDefaultPosition(index)
                    Log.d(TAG, "replaceAll: 跳转到索引 $index")
                } else {
                    Log.w(TAG, "replaceAll: 未找到指定歌曲，播放第一首")
                    currentPlayer.seekToDefaultPosition(0)
                }
                currentPlayer.play()
            } catch (e: Exception) {
                Log.e(TAG, "replaceAll: 设置媒体项失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "replaceAll: 执行失败", e)
        }
    }

    override fun play(mediaId: String) {
        player?.let {
            val index = _playlist.value?.indexOfFirst { item -> item.mediaId == mediaId } ?: -1
            if (index >= 0) {
                it.seekToDefaultPosition(index)
                it.play()
            }
        }
    }

    override fun delete(song: MediaItem) {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            val index = currentPlaylist.indexOfFirst { item -> item.mediaId == song.mediaId }
            if (index >= 0) {
                currentPlaylist.removeAt(index)
                _playlist.value = currentPlaylist

                it.removeMediaItem(index)
            }
        }
    }

    override fun clearPlaylist() {
        player?.let {
            _playlist.value = emptyList()
            it.clearMediaItems()
        }
    }

    override fun playPause() {
        ensureServiceStarted()
        player?.let {
            // 参考ponymusic的实现，检查播放列表
            if (it.mediaItemCount == 0) {
                Log.w(TAG, "playPause: 播放列表为空")
                return
            }

            when (it.playbackState) {
                Player.STATE_IDLE -> {
                    it.prepare()
                    Log.d(TAG, "playPause: 准备播放")
                }
                Player.STATE_BUFFERING -> {
                    it.stop()
                    Log.d(TAG, "playPause: 停止缓冲")
                }
                Player.STATE_READY -> {
                    if (it.isPlaying) {
                        it.pause()
                        _playState.value = PlayState.Pause
                        Log.d(TAG, "playPause: 暂停播放")
                    } else {
                        it.play()
                        _playState.value = PlayState.Playing
                        Log.d(TAG, "playPause: 开始播放")
                    }
                }
                Player.STATE_ENDED -> {
                    it.seekToNextMediaItem()
                    it.prepare()
                    Log.d(TAG, "playPause: 播放下一首")
                }
                else -> {
                    Log.w(TAG, "playPause: 未知播放状态 ${it.playbackState}")
                }
            }
        }
    }

    override fun next() {
        player?.let {
            // 参考ponymusic的简单实现
            if (it.mediaItemCount == 0) return
            it.seekToNextMediaItem()
            it.prepare()
            _playProgress.value = 0
        }
    }

    override fun prev() {
        player?.let {
            // 参考ponymusic的简单实现
            if (it.mediaItemCount == 0) return
            it.seekToPreviousMediaItem()
            it.prepare()
            _playProgress.value = 0
        }
    }

    override fun seekTo(position: Int) {
        player?.seekTo(position.toLong())
    }

    override fun getAudioSessionId(): Int {
        // 使用反射获取audioSessionId
        return try {
            val player = player ?: return 0
            val method = player.javaClass.getMethod("getAudioSessionId")
            method.invoke(player) as? Int ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取audioSessionId失败", e)
            0
        }
    }

    override fun setPlayMode(mode: PlayMode) {
        // 保存播放模式到SharedPreferences - 参考ponymusic实现
        val sharedPref = context.getSharedPreferences("music_player_pref", Context.MODE_PRIVATE)
        sharedPref.edit().putInt("play_mode", mode.value).apply()

        _playMode.value = mode
        player?.let {
            when (mode) {
                PlayMode.Loop -> {
                    it.repeatMode = Player.REPEAT_MODE_ALL
                    it.shuffleModeEnabled = false
                    Log.d(TAG, "设置播放模式: 列表循环")
                }
                PlayMode.Shuffle -> {
                    it.repeatMode = Player.REPEAT_MODE_ALL
                    it.shuffleModeEnabled = true
                    Log.d(TAG, "设置播放模式: 随机播放")
                }
                PlayMode.Single -> {
                    it.repeatMode = Player.REPEAT_MODE_ONE
                    it.shuffleModeEnabled = false
                    Log.d(TAG, "设置播放模式: 单曲循环")
                }
            }
        }
    }

    override fun stop() {
        player?.stop()
    }

    override fun shufflePlaylist() {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            if (currentPlaylist.size <= 1) return

            // 获取当前播放的歌曲
            val currentIndex = it.currentMediaItemIndex
            val currentSong = if (currentIndex >= 0 && currentIndex < currentPlaylist.size) {
                currentPlaylist[currentIndex]
            } else null

            // 移除当前播放的歌曲
            if (currentSong != null) {
                currentPlaylist.removeAt(currentIndex)
            }

            // 随机打乱剩余歌曲
            currentPlaylist.shuffle()

            // 将当前播放的歌曲放在第一位
            if (currentSong != null) {
                currentPlaylist.add(0, currentSong)
            }

            // 更新播放列表
            _playlist.value = currentPlaylist
            it.clearMediaItems()
            it.setMediaItems(currentPlaylist)
            it.prepare()

            // 播放第一首（当前歌曲）
            if (currentSong != null) {
                it.seekToDefaultPosition(0)
                it.play()
            }
        }
    }

    override fun removeFromPlaylist(position: Int) {
        player?.let {
            val currentPlaylist = _playlist.value?.toMutableList() ?: return
            if (position < 0 || position >= currentPlaylist.size) return

            // 移除指定位置的歌曲
            currentPlaylist.removeAt(position)
            _playlist.value = currentPlaylist

            // 从播放器中移除
            it.removeMediaItem(position)
        }
    }

    override fun getCurrentPlaylist(): List<MediaItem> {
        return _playlist.value ?: emptyList()
    }

    override fun getCurrentIndex(): Int {
        return player?.currentMediaItemIndex ?: -1
    }

    override fun getCurrentPosition(): Long {
        return player?.currentPosition ?: 0
    }

    override fun getDuration(): Long {
        return player?.duration ?: 0
    }

    override fun playAtIndex(index: Int) {
        player?.let {
            if (index >= 0 && index < (_playlist.value?.size ?: 0)) {
                it.seekToDefaultPosition(index)
                it.play()
            }
        }
    }

    private fun ensureServiceStarted() {
        try {
            val intent = Intent(context, UnifiedPlaybackService::class.java)
            context.startService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start service", e)
        }
    }
}
